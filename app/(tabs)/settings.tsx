import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity,
  Switch,
  ActionSheetIOS,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { 
  Globe, 
  Palette, 
  Shield, 
  User, 
  Info, 
  ChevronRight,
  Bell 
} from 'lucide-react-native';

import { useTheme } from '@/src/theme';
import { useThemeStore } from '@/src/store';
import { useLanguage } from '@/src/hooks/useLanguage';
import { Card } from '@/src/components/ui/Card';

interface SettingRowProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  value?: string;
  onPress?: () => void;
  showArrow?: boolean;
  rightElement?: React.ReactNode;
}

function SettingRow({ 
  icon, 
  title, 
  subtitle, 
  value, 
  onPress, 
  showArrow = true,
  rightElement 
}: SettingRowProps) {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    row: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.md
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.primaryLight,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.md
    },
    content: {
      flex: 1
    },
    title: {
      fontSize: theme.typography.sizes.base,
      fontWeight: theme.typography.weights.medium,
      color: theme.colors.text,
      marginBottom: subtitle ? 2 : 0
    },
    subtitle: {
      fontSize: theme.typography.sizes.sm,
      color: theme.colors.textSecondary
    },
    rightContent: {
      flexDirection: 'row',
      alignItems: 'center'
    },
    value: {
      fontSize: theme.typography.sizes.base,
      color: theme.colors.textSecondary,
      marginRight: showArrow ? theme.spacing.sm : 0
    }
  });

  const content = (
    <View style={styles.row}>
      <View style={styles.iconContainer}>
        {icon}
      </View>
      <View style={styles.content}>
        <Text style={styles.title}>{title}</Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>
      <View style={styles.rightContent}>
        {value && <Text style={styles.value}>{value}</Text>}
        {rightElement}
        {showArrow && onPress && (
          <ChevronRight size={20} color={theme.colors.textMuted} />
        )}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} accessibilityRole="button">
        {content}
      </TouchableOpacity>
    );
  }

  return content;
}

export default function SettingsScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const { theme: themeMode, setTheme } = useThemeStore();
  const { currentLanguage, changeLanguage, availableLanguages } = useLanguage();
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background
    },
    header: {
      padding: theme.spacing.lg,
      paddingBottom: theme.spacing.md
    },
    title: {
      fontSize: theme.typography.sizes['3xl'],
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text
    },
    content: {
      paddingHorizontal: theme.spacing.lg
    },
    section: {
      marginBottom: theme.spacing.lg
    },
    sectionTitle: {
      fontSize: theme.typography.sizes.sm,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.textSecondary,
      textTransform: 'uppercase',
      letterSpacing: 0.5,
      marginBottom: theme.spacing.sm,
      marginLeft: theme.spacing.xs
    }
  });

  const handleLanguageChange = () => {
    if (Platform.OS === 'ios') {
      const options = availableLanguages.map(lang => lang.name);
      options.push(t('common.cancel'));
      
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex: options.length - 1,
          title: t('settings.language')
        },
        (buttonIndex) => {
          if (buttonIndex < availableLanguages.length) {
            changeLanguage(availableLanguages[buttonIndex].code);
          }
        }
      );
    }
    // For Android, you could implement a modal picker
  };

  const handleThemeChange = () => {
    if (Platform.OS === 'ios') {
      const options = [
        t('themes.light'),
        t('themes.dark'),
        t('themes.system'),
        t('common.cancel')
      ];
      
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex: 3,
          title: t('settings.theme')
        },
        (buttonIndex) => {
          const themes: Array<'light' | 'dark' | 'system'> = ['light', 'dark', 'system'];
          if (buttonIndex < 3) {
            setTheme(themes[buttonIndex]);
          }
        }
      );
    }
  };

  const getCurrentLanguageName = () => {
    const lang = availableLanguages.find(l => l.code === currentLanguage);
    return lang?.name || 'English';
  };

  const getCurrentThemeName = () => {
    return t(`themes.${themeMode}`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>{t('settings.title')}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preferences</Text>
          <Card padding="none">
            <SettingRow
              icon={<Globe size={18} color={theme.colors.primary} />}
              title={t('settings.language')}
              value={getCurrentLanguageName()}
              onPress={handleLanguageChange}
            />
            <SettingRow
              icon={<Palette size={18} color={theme.colors.primary} />}
              title={t('settings.theme')}
              value={getCurrentThemeName()}
              onPress={handleThemeChange}
            />
            <SettingRow
              icon={<Bell size={18} color={theme.colors.primary} />}
              title={t('settings.notifications')}
              rightElement={
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                  thumbColor={notificationsEnabled ? theme.colors.primary : theme.colors.textMuted}
                  trackColor={{ 
                    false: theme.colors.border, 
                    true: theme.colors.primaryLight 
                  }}
                />
              }
              showArrow={false}
            />
          </Card>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>
          <Card padding="none">
            <SettingRow
              icon={<Shield size={18} color={theme.colors.primary} />}
              title={t('settings.privacy')}
              subtitle={t('settings.privacyDefault')}
              value="Public"
            />
          </Card>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <Card padding="none">
            <SettingRow
              icon={<User size={18} color={theme.colors.primary} />}
              title={t('settings.account')}
              subtitle="<EMAIL>"
            />
          </Card>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          <Card padding="none">
            <SettingRow
              icon={<Info size={18} color={theme.colors.primary} />}
              title={t('settings.about')}
              subtitle={t('settings.version')}
              value="1.0.0"
              showArrow={false}
            />
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}