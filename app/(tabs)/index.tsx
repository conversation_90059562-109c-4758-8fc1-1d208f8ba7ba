import React from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  RefreshControl
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { LinearGradient } from 'expo-linear-gradient';
import { Eye, Lock } from 'lucide-react-native';

import { useTheme } from '@/src/theme';
import { Card } from '@/src/components/ui/Card';
import { LoadingSpinner } from '@/src/components/ui/LoadingSpinner';
import { services } from '@/src/services';
import type { Project } from '@/src/entities';

export default function HomeScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const router = useRouter();

  const { data: projects, isLoading, refetch, isFetching } = useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const response = await services.projects.listProjects();
      if (!response.success) throw new Error(response.error);
      return response.data || [];
    }
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background
    },
    header: {
      padding: theme.spacing.lg,
      paddingBottom: theme.spacing.md
    },
    headerTitle: {
      fontSize: theme.typography.sizes['3xl'],
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.xs
    },
    headerSubtitle: {
      fontSize: theme.typography.sizes.base,
      color: theme.colors.textSecondary,
      lineHeight: 22
    },
    list: {
      paddingHorizontal: theme.spacing.lg
    },
    projectCard: {
      marginBottom: theme.spacing.md
    },
    projectImage: {
      width: '100%',
      height: 200,
      borderRadius: theme.borderRadius.lg,
      marginBottom: theme.spacing.md
    },
    projectContent: {
      padding: theme.spacing.md
    },
    projectHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.sm
    },
    projectTitle: {
      flex: 1,
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.text,
      marginRight: theme.spacing.sm
    },
    privacyBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderRadius: theme.borderRadius.full,
      backgroundColor: theme.colors.primaryLight
    },
    privacyText: {
      fontSize: theme.typography.sizes.xs,
      fontWeight: theme.typography.weights.medium,
      color: theme.colors.primary,
      marginLeft: theme.spacing.xs
    },
    projectDescription: {
      fontSize: theme.typography.sizes.base,
      color: theme.colors.textSecondary,
      lineHeight: 20,
      marginBottom: theme.spacing.sm
    },
    projectFooter: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center'
    },
    sceneCount: {
      fontSize: theme.typography.sizes.sm,
      color: theme.colors.textMuted
    },
    gradientOverlay: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 80,
      borderRadius: theme.borderRadius.lg,
      justifyContent: 'flex-end',
      padding: theme.spacing.md
    },
    overlayTitle: {
      fontSize: theme.typography.sizes.lg,
      fontWeight: theme.typography.weights.semibold,
      color: '#ffffff',
      textShadowColor: 'rgba(0, 0, 0, 0.5)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.lg
    },
    emptyText: {
      fontSize: theme.typography.sizes.lg,
      color: theme.colors.textSecondary,
      textAlign: 'center'
    }
  });

  const handleProjectPress = (project: Project) => {
    router.push(`/project/${project.id}`);
  };

  const renderProject = ({ item: project }: { item: Project }) => (
    <TouchableOpacity
      style={styles.projectCard}
      onPress={() => handleProjectPress(project)}
      accessibilityRole="button"
      accessibilityLabel={`${project.title}. ${project.description}`}
    >
      <Card padding="none">
        <View>
          {project.imageUrl && (
            <View>
              <Image
                source={{ uri: project.imageUrl }}
                style={styles.projectImage}
                resizeMode="cover"
              />
              <LinearGradient
                colors={['transparent', 'rgba(0,0,0,0.6)']}
                style={styles.gradientOverlay}
              >
                <Text style={styles.overlayTitle}>{project.title}</Text>
              </LinearGradient>
            </View>
          )}
          <View style={styles.projectContent}>
            <View style={styles.projectHeader}>
              <Text style={styles.projectTitle}>{project.title}</Text>
              <View style={styles.privacyBadge}>
                {project.privacy === 'public' ? (
                  <Eye size={12} color={theme.colors.primary} />
                ) : (
                  <Lock size={12} color={theme.colors.primary} />
                )}
                <Text style={styles.privacyText}>
                  {t(`home.privacy${project.privacy.charAt(0).toUpperCase() + project.privacy.slice(1)}`)}
                </Text>
              </View>
            </View>

            <Text style={styles.projectDescription}>
              {project.description}
            </Text>

            <View style={styles.projectFooter}>
              <Text style={styles.sceneCount}>
                {project.scenes.length} {project.scenes.length === 1 ? 'scene' : 'scenes'}
              </Text>
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>{t('home.title')}</Text>
        <Text style={styles.headerSubtitle}>{t('home.subtitle')}</Text>
      </View>

      {projects && projects.length > 0 ? (
        <FlatList
          data={projects}
          renderItem={renderProject}
          keyExtractor={item => item.id}
          style={styles.list}
          refreshControl={
            <RefreshControl
              refreshing={isFetching}
              onRefresh={refetch}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>{t('home.noProjects')}</Text>
        </View>
      )}
    </SafeAreaView>
  );
}
