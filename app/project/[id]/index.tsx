import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  Image,
  TouchableOpacity 
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeft, 
  Users, 
  Shield, 
  Calendar,
  Play,
  Settings
} from 'lucide-react-native';

import { useTheme } from '@/src/theme';
import { Card } from '@/src/components/ui/Card';
import { Button } from '@/src/components/ui/Button';
import { LoadingSpinner } from '@/src/components/ui/LoadingSpinner';
import { services } from '@/src/services';

export default function ProjectDetailScreen() {
  const { theme } = useTheme();
  const { t } = useTranslation();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();

  const { data: project, isLoading } = useQuery({
    queryKey: ['project', id],
    queryFn: async () => {
      const response = await services.projects.getProject(id!);
      if (!response.success) throw new Error(response.error);
      return response.data;
    },
    enabled: !!id
  });

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing.lg,
      paddingBottom: theme.spacing.md
    },
    backButton: {
      marginRight: theme.spacing.md
    },
    headerTitle: {
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.text
    },
    content: {
      paddingHorizontal: theme.spacing.lg
    },
    heroImage: {
      width: '100%',
      height: 240,
      borderRadius: theme.borderRadius.lg,
      marginBottom: theme.spacing.lg
    },
    title: {
      fontSize: theme.typography.sizes['3xl'],
      fontWeight: theme.typography.weights.bold,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm
    },
    metaRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.sm
    },
    metaText: {
      fontSize: theme.typography.sizes.sm,
      color: theme.colors.textSecondary,
      marginLeft: theme.spacing.xs
    },
    description: {
      fontSize: theme.typography.sizes.base,
      color: theme.colors.textSecondary,
      lineHeight: 22,
      marginBottom: theme.spacing.lg
    },
    section: {
      marginBottom: theme.spacing.lg
    },
    sectionTitle: {
      fontSize: theme.typography.sizes.xl,
      fontWeight: theme.typography.weights.semibold,
      color: theme.colors.text,
      marginBottom: theme.spacing.md
    },
    sceneItem: {
      padding: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.borderLight
    },
    sceneHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: theme.spacing.xs
    },
    sceneNumber: {
      width: 24,
      height: 24,
      borderRadius: 12,
      backgroundColor: theme.colors.primary,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.sm
    },
    sceneNumberText: {
      fontSize: theme.typography.sizes.sm,
      fontWeight: theme.typography.weights.semibold,
      color: '#ffffff'
    },
    scenePrompt: {
      flex: 1,
      fontSize: theme.typography.sizes.base,
      fontWeight: theme.typography.weights.medium,
      color: theme.colors.text
    },
    sceneGuidance: {
      fontSize: theme.typography.sizes.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
      marginLeft: 36
    },
    sceneDuration: {
      fontSize: theme.typography.sizes.xs,
      color: theme.colors.textMuted,
      marginTop: theme.spacing.xs,
      marginLeft: 36
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.lg
    },
    button: {
      flex: 1
    }
  });

  const handleBack = () => {
    router.back();
  };

  const handleParticipate = () => {
    router.push(`/project/${id}/participate`);
  };

  const handleModerate = () => {
    router.push(`/moderation/${id}`);
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!project) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Project not found</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton} 
          onPress={handleBack}
          accessibilityRole="button"
          accessibilityLabel={t('common.back')}
        >
          <ArrowLeft size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{t('project.title')}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {project.imageUrl && (
          <Image 
            source={{ uri: project.imageUrl }} 
            style={styles.heroImage}
            resizeMode="cover"
          />
        )}

        <Text style={styles.title}>{project.title}</Text>

        <View style={styles.metaRow}>
          <Users size={16} color={theme.colors.textSecondary} />
          <Text style={styles.metaText}>{t('project.owner')}</Text>
        </View>

        <View style={styles.metaRow}>
          <Shield size={16} color={theme.colors.textSecondary} />
          <Text style={styles.metaText}>
            {project.privacy === 'public' ? t('home.privacyPublic') : t('home.privacyApproved')}
          </Text>
        </View>

        {project.deadline && (
          <View style={styles.metaRow}>
            <Calendar size={16} color={theme.colors.textSecondary} />
            <Text style={styles.metaText}>
              {new Date(project.deadline).toLocaleDateString()}
            </Text>
          </View>
        )}

        <Text style={styles.description}>{project.description}</Text>

        {project.goals && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('project.goals')}</Text>
            <Card>
              <Text style={[styles.description, { marginBottom: 0 }]}>
                {project.goals}
              </Text>
            </Card>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {t('project.scenes')} ({project.scenes.length})
          </Text>
          <Card padding="none">
            {project.scenes.map((scene, index) => (
              <View key={scene.id} style={styles.sceneItem}>
                <View style={styles.sceneHeader}>
                  <View style={styles.sceneNumber}>
                    <Text style={styles.sceneNumberText}>{scene.order + 1}</Text>
                  </View>
                  <Text style={styles.scenePrompt}>{scene.prompt}</Text>
                </View>
                {scene.guidance && (
                  <Text style={styles.sceneGuidance}>{scene.guidance}</Text>
                )}
                {scene.durationLimitSec && (
                  <Text style={styles.sceneDuration}>
                    Max duration: {scene.durationLimitSec}s
                  </Text>
                )}
              </View>
            ))}
          </Card>
        </View>
      </ScrollView>

      <View style={styles.buttonContainer}>
        <Button
          title={t('project.participate')}
          onPress={handleParticipate}
          style={styles.button}
        />
        {/* Show moderate button if user is owner - this would be determined by auth context */}
        <Button
          title={t('project.moderate')}
          variant="outline"
          onPress={handleModerate}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
}