const AsyncStorage = require('@react-native-async-storage/async-storage').default;

async function resetMockData() {
  try {
    console.log('Resetting mock data...');
    
    // Get all keys
    const keys = await AsyncStorage.getAllKeys();
    
    // Filter for Yiqqi keys
    const yiqqiKeys = keys.filter(key => key.startsWith('yiqqi_'));
    
    if (yiqqiKeys.length > 0) {
      // Remove all Yiqqi data
      await AsyncStorage.multiRemove(yiqqiKeys);
      console.log(`Removed ${yiqqiKeys.length} stored items`);
    } else {
      console.log('No stored data found');
    }
    
    console.log('Mock data reset complete!');
  } catch (error) {
    console.error('Error resetting mock data:', error);
  }
}

resetMockData();