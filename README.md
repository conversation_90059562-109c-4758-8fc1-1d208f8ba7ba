# Yiqqi - Collaborative Video App

A React Native app built with Expo that enables collaborative video storytelling, inspired by the power of community-driven content creation.

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Expo CLI (`npm install -g @expo/cli`)
- iOS Simulator (Mac) or Android Emulator

### Installation & Setup

```bash
# Install dependencies
npm install

# Start the development server
npm run dev

# Or use specific platforms
npm run ios     # iOS Simulator
npm run android # Android Emulator
```

## 📱 App Features

### Core Functionality
- **Project Discovery**: Browse collaborative video projects with rich metadata
- **Scene-based Participation**: Contribute to projects through guided scene prompts  
- **Video Capture & Upload**: Record or import videos with resumable upload progress
- **Content Moderation**: Project owners can review and approve/reject submissions
- **Movie Compilation**: Automated rendering of collaborative projects into shareable movies
- **Multi-language Support**: English, French, and Spanish translations

### Technical Features
- **Tab Navigation**: Home, Participate, My Movies, Notifications, Settings
- **Privacy Controls**: Public and approval-required project types
- **Age Verification**: 13+ age gating with consent management
- **Dark/Light Themes**: System-aware theming with manual override
- **Offline Support**: Data persistence via AsyncStorage
- **Background Uploads**: Upload progress maintained across app states

## 🏗 Architecture

### Project Structure
```
src/
├── entities/           # TypeScript domain models
├── services/           # Business logic with adapter pattern
│   ├── auth.ts        # Authentication service
│   ├── projects.ts    # Project management
│   ├── participation.ts # User contributions
│   ├── uploads.ts     # File upload handling
│   └── ...
├── components/ui/      # Reusable UI components
├── theme/             # Design system & theming
├── store/             # Zustand state management
├── i18n/              # Internationalization
└── hooks/             # Custom React hooks

app/                   # expo-router file-based routing
├── (tabs)/            # Tab navigation screens
├── project/[id]/      # Project detail screens
└── auth/              # Authentication flows
```

### Service Architecture
The app uses an adapter pattern for all external services:

```typescript
// All services implement interfaces for easy swapping
export interface ProjectsService {
  listProjects(): Promise<ApiResponse<Project[]>>;
  getProject(id: string): Promise<ApiResponse<Project>>;
  // ...
}

// Mock implementations for development
export class MockProjectsService implements ProjectsService {
  // Local data with AsyncStorage persistence
}

// Production implementations would connect to real APIs
export class ApiProjectsService implements ProjectsService {
  // HTTP calls to backend services
}
```

### State Management
- **TanStack Query**: Server state, caching, background sync
- **Zustand**: UI state (theme, uploads, toasts)
- **AsyncStorage**: Persistent local storage

### Internationalization
```typescript
// Add new languages by creating locale files
src/i18n/locales/[language].json

// Use translations in components
const { t } = useTranslation();
<Text>{t('home.title')}</Text>
```

## 🎨 Design System

### Theme Configuration
```typescript
const theme = {
  colors: {
    primary: '#14b8a6',    // Teal brand color
    secondary: '#a855f7',   // Purple accent
    // ... full color palette
  },
  spacing: {
    xs: 4, sm: 8, md: 16, lg: 24, xl: 32, xxl: 48
  },
  typography: {
    sizes: { xs: 12, sm: 14, base: 16, lg: 18, xl: 20, '2xl': 24 }
  }
}
```

### Responsive Design
- Mobile-first approach with adaptive layouts
- Touch-friendly tap targets (44px minimum)
- VoiceOver/TalkBack accessibility support

## 🧪 Testing

### Running Tests
```bash
npm test              # Run unit tests
npm run test:watch    # Watch mode
npm run typecheck     # TypeScript checking
npm run lint          # ESLint
```

### Test Structure
- **Unit Tests**: Service layer and utility functions
- **Component Tests**: React Native Testing Library
- **E2E Tests**: Detox configuration included

## 📦 Building & Deployment

### EAS Build
```bash
# Install EAS CLI
npm install -g eas-cli

# Configure project
eas build:configure

# Build for development
eas build --profile development --platform ios

# Build for production
eas build --profile production --platform all
```

### Environment Configuration
The app includes EAS profiles for:
- **Development**: Development client with debugging
- **Preview**: Internal distribution for testing  
- **Production**: App store ready builds

## 🔧 Development Tools

### Service Adapter Swapping
```typescript
// Switch from mock to real services
import { services } from '@/src/services';

services.configure({
  auth: new ProductionAuthService(),
  projects: new ApiProjectsService(),
  // ... other services
});
```

### Language Management
```typescript
// Add new language
1. Create src/i18n/locales/[code].json
2. Add to availableLanguages in useLanguage hook
3. Update language selector in settings
```

### Mock Data Reset
```bash
npm run reset-data    # Clear all stored mock data
```

## 🔐 Privacy & Compliance

### Age Verification
- 13+ age requirement enforced at registration
- Date of birth validation with friendly error messages
- Privacy consent acknowledgment required

### Data Handling  
- All PII clearly identified in privacy flows
- Project privacy settings (public/approval required)
- User content consent for video sharing

## 🚀 Deployment Ready

### Production Checklist
- [ ] Configure real authentication service
- [ ] Set up video upload backend with resumable uploads
- [ ] Implement push notifications (FCM/APNs)
- [ ] Add video processing pipeline
- [ ] Set up analytics service
- [ ] Configure error reporting
- [ ] Add performance monitoring

### EAS Configuration
The included `eas.json` provides ready-to-use build profiles:
- Development builds for testing
- Preview builds for internal distribution
- Production builds for app stores

## 📄 License

This project is intended for educational and development purposes. See LICENSE file for details.

---

Built with ❤️ using Expo, React Native, and TypeScript.