import { MockAuthService } from '../../services/auth';

describe('AuthService', () => {
  let authService: MockAuthService;

  beforeEach(() => {
    authService = new MockAuthService();
  });

  describe('login', () => {
    it('should create a new user for first-time login', async () => {
      const email = '<EMAIL>';
      const result = await authService.login(email);

      expect(result.success).toBe(true);
      expect(result.data?.email).toBe(email);
      expect(result.data?.name).toBe('test');
    });

    it('should return existing user for subsequent logins', async () => {
      const email = '<EMAIL>';
      
      // First login
      const firstResult = await authService.login(email);
      const firstUserId = firstResult.data?.id;

      // Second login
      const secondResult = await authService.login(email);
      
      expect(secondResult.success).toBe(true);
      expect(secondResult.data?.id).toBe(firstUserId);
    });
  });

  describe('ageCheck', () => {
    it('should return eligible for users 13 and older', async () => {
      const thirteenYearsAgo = new Date();
      thirteenYearsAgo.setFullYear(thirteenYearsAgo.getFullYear() - 13);
      
      const result = await authService.ageCheck(thirteenYearsAgo.toISOString());
      
      expect(result.success).toBe(true);
      expect(result.data?.eligible).toBe(true);
    });

    it('should return not eligible for users under 13', async () => {
      const twelveYearsAgo = new Date();
      twelveYearsAgo.setFullYear(twelveYearsAgo.getFullYear() - 12);
      
      const result = await authService.ageCheck(twelveYearsAgo.toISOString());
      
      expect(result.success).toBe(true);
      expect(result.data?.eligible).toBe(false);
    });
  });
});