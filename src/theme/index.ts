import { useColorScheme } from 'react-native';
import { useThemeStore } from '../store';

export const colors = {
  primary: {
    50: '#f0fdfa',
    100: '#ccfbf1',
    200: '#99f6e4',
    300: '#5eead4',
    400: '#2dd4bf',
    500: '#14b8a6',
    600: '#0d9488',
    700: '#0f766e',
    800: '#115e59',
    900: '#134e4a'
  },
  secondary: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7',
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87'
  },
  accent: {
    50: '#fff7ed',
    100: '#ffedd5',
    200: '#fed7aa',
    300: '#fdba74',
    400: '#fb923c',
    500: '#f97316',
    600: '#ea580c',
    700: '#c2410c',
    800: '#9a3412',
    900: '#7c2d12'
  },
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717'
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d'
  },
  warning: {
    50: '#fefce8',
    100: '#fef9c3',
    200: '#fef08a',
    300: '#fde047',
    400: '#facc15',
    500: '#eab308',
    600: '#ca8a04',
    700: '#a16207',
    800: '#854d0e',
    900: '#713f12'
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d'
  }
};

export const lightTheme = {
  colors: {
    background: colors.neutral[50],
    surface: '#ffffff',
    surfaceSecondary: colors.neutral[100],
    primary: colors.primary[600],
    primaryLight: colors.primary[100],
    secondary: colors.secondary[600],
    accent: colors.accent[500],
    text: colors.neutral[900],
    textSecondary: colors.neutral[600],
    textMuted: colors.neutral[500],
    border: colors.neutral[200],
    borderLight: colors.neutral[100],
    success: colors.success[600],
    warning: colors.warning[500],
    error: colors.error[600],
    overlay: 'rgba(0, 0, 0, 0.5)'
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48
  },
  borderRadius: {
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    full: 9999
  },
  typography: {
    sizes: {
      xs: 12,
      sm: 14,
      base: 16,
      lg: 18,
      xl: 20,
      '2xl': 24,
      '3xl': 30,
      '4xl': 36
    },
    weights: {
      normal: '400' as const,
      medium: '500' as const,
      semibold: '600' as const,
      bold: '700' as const
    }
  }
};

export const darkTheme = {
  ...lightTheme,
  colors: {
    ...lightTheme.colors,
    background: colors.neutral[900],
    surface: colors.neutral[800],
    surfaceSecondary: colors.neutral[700],
    primary: colors.primary[400],
    primaryLight: colors.primary[900],
    text: colors.neutral[100],
    textSecondary: colors.neutral[300],
    textMuted: colors.neutral[400],
    border: colors.neutral[700],
    borderLight: colors.neutral[800],
    overlay: 'rgba(0, 0, 0, 0.7)'
  }
};

export function useTheme() {
  const systemColorScheme = useColorScheme();
  const { theme: userTheme } = useThemeStore();

  const activeTheme = userTheme === 'system' 
    ? (systemColorScheme === 'dark' ? 'dark' : 'light')
    : userTheme;

  return {
    theme: activeTheme === 'dark' ? darkTheme : lightTheme,
    isDark: activeTheme === 'dark'
  };
}

export type Theme = typeof lightTheme;