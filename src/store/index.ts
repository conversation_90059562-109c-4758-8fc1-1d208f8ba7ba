import { create } from 'zustand';

interface ThemeState {
  theme: 'light' | 'dark' | 'system';
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}

export const useThemeStore = create<ThemeState>((set) => ({
  theme: 'system',
  setTheme: (theme) => set({ theme })
}));

interface UIState {
  isLoading: boolean;
  toasts: Array<{ id: string; message: string; type: 'success' | 'error' | 'info' }>;
  setLoading: (loading: boolean) => void;
  addToast: (message: string, type?: 'success' | 'error' | 'info') => void;
  removeToast: (id: string) => void;
}

export const useUIStore = create<UIState>((set, get) => ({
  isLoading: false,
  toasts: [],
  setLoading: (isLoading) => set({ isLoading }),
  addToast: (message, type = 'info') => {
    const id = Date.now().toString();
    set(state => ({
      toasts: [...state.toasts, { id, message, type }]
    }));
    // Auto remove after 3 seconds
    setTimeout(() => {
      get().removeToast(id);
    }, 3000);
  },
  removeToast: (id) => set(state => ({
    toasts: state.toasts.filter(toast => toast.id !== id)
  }))
}));

interface CameraState {
  hasPermission: boolean | null;
  isRecording: boolean;
  recordingDuration: number;
  setPermission: (permission: boolean) => void;
  setRecording: (recording: boolean) => void;
  setRecordingDuration: (duration: number) => void;
}

export const useCameraStore = create<CameraState>((set) => ({
  hasPermission: null,
  isRecording: false,
  recordingDuration: 0,
  setPermission: (hasPermission) => set({ hasPermission }),
  setRecording: (isRecording) => set({ isRecording }),
  setRecordingDuration: (recordingDuration) => set({ recordingDuration })
}));

interface UploadState {
  activeUploads: Map<string, { progress: number; status: string }>;
  updateUpload: (id: string, progress: number, status: string) => void;
  removeUpload: (id: string) => void;
}

export const useUploadStore = create<UploadState>((set) => ({
  activeUploads: new Map(),
  updateUpload: (id, progress, status) => set(state => {
    const newUploads = new Map(state.activeUploads);
    newUploads.set(id, { progress, status });
    return { activeUploads: newUploads };
  }),
  removeUpload: (id) => set(state => {
    const newUploads = new Map(state.activeUploads);
    newUploads.delete(id);
    return { activeUploads: newUploads };
  })
}));