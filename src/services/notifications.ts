import { Notification, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface NotificationsService {
  subscribe(userId: string): Promise<ApiResponse<void>>;
  list(userId: string): Promise<ApiResponse<Notification[]>>;
  markRead(notificationId: string): Promise<ApiResponse<void>>;
  markAllRead(userId: string): Promise<ApiResponse<void>>;
}

export class MockNotificationsService implements NotificationsService {
  async subscribe(userId: string): Promise<ApiResponse<void>> {
    // In a real implementation, this would register for push notifications
    return { success: true };
  }

  async list(userId: string): Promise<ApiResponse<Notification[]>> {
    try {
      const notifications = await mockStore.get<Notification[]>('notifications') || [];
      const userNotifications = notifications.filter(n => n.userId === userId);
      
      return {
        success: true,
        data: userNotifications.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load notifications'
      };
    }
  }

  async markRead(notificationId: string): Promise<ApiResponse<void>> {
    try {
      const notifications = await mockStore.get<Notification[]>('notifications') || [];
      const notificationIndex = notifications.findIndex(n => n.id === notificationId);
      
      if (notificationIndex >= 0) {
        notifications[notificationIndex].read = true;
        await mockStore.set('notifications', notifications);
      }
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to mark notification as read'
      };
    }
  }

  async markAllRead(userId: string): Promise<ApiResponse<void>> {
    try {
      const notifications = await mockStore.get<Notification[]>('notifications') || [];
      const updatedNotifications = notifications.map(n => 
        n.userId === userId ? { ...n, read: true } : n
      );
      
      await mockStore.set('notifications', updatedNotifications);
      
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to mark all notifications as read'
      };
    }
  }
}