import { ModerationItem, Clip, Participation, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface SetVerdictInput {
  itemId: string;
  verdict: 'approve' | 'reject';
  reason?: string;
}

export interface ModerationService {
  listPendingClips(projectId: string): Promise<ApiResponse<Clip[]>>;
  setVerdict(input: SetVerdictInput): Promise<ApiResponse<void>>;
  getModerationHistory(projectId: string): Promise<ApiResponse<ModerationItem[]>>;
}

export class MockModerationService implements ModerationService {
  async listPendingClips(projectId: string): Promise<ApiResponse<Clip[]>> {
    try {
      const clips = await mockStore.get<Clip[]>('clips') || [];
      const participations = await mockStore.get<Participation[]>('participations') || [];
      
      // Get clips that are submitted but not yet moderated
      const pendingClips = clips.filter(clip => {
        const participation = participations.find(p => p.sceneId === clip.sceneId && p.userId === clip.ownerId);
        return participation?.projectId === projectId && clip.status === 'uploaded';
      });
      
      return {
        success: true,
        data: pendingClips
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load pending clips'
      };
    }
  }

  async setVerdict(input: SetVerdictInput): Promise<ApiResponse<void>> {
    try {
      // Update clip status
      const clips = await mockStore.get<Clip[]>('clips') || [];
      const clipIndex = clips.findIndex(c => c.id === input.itemId);
      
      if (clipIndex >= 0) {
        clips[clipIndex].status = input.verdict === 'approve' ? 'approved' : 'rejected';
        await mockStore.set('clips', clips);
      }
      
      // Update participation status
      const participations = await mockStore.get<Participation[]>('participations') || [];
      const participationIndex = participations.findIndex(p => 
        clips[clipIndex] && p.sceneId === clips[clipIndex].sceneId && p.userId === clips[clipIndex].ownerId
      );
      
      if (participationIndex >= 0) {
        participations[participationIndex].status = input.verdict === 'approve' ? 'approved' : 'rejected';
        participations[participationIndex].notes = input.reason;
        await mockStore.set('participations', participations);
      }
      
      // Create moderation record
      const moderationItem: ModerationItem = {
        id: `moderation_${Date.now()}`,
        targetType: 'clip',
        targetId: input.itemId,
        verdict: input.verdict,
        reason: input.reason,
        createdAt: new Date().toISOString(),
        reviewerId: 'current_user' // Would come from auth context
      };
      
      const moderationHistory = await mockStore.get<ModerationItem[]>('moderation_history') || [];
      moderationHistory.push(moderationItem);
      await mockStore.set('moderation_history', moderationHistory);
      
      return {
        success: true
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to set verdict'
      };
    }
  }

  async getModerationHistory(projectId: string): Promise<ApiResponse<ModerationItem[]>> {
    try {
      const history = await mockStore.get<ModerationItem[]>('moderation_history') || [];
      
      // Filter by project - would need better data structure in real implementation
      return {
        success: true,
        data: history
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load moderation history'
      };
    }
  }
}