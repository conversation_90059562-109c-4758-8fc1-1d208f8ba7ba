export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
}

export interface AnalyticsService {
  track(event: string, properties?: Record<string, any>): Promise<void>;
  identify(userId: string, properties?: Record<string, any>): Promise<void>;
}

export class MockAnalyticsService implements AnalyticsService {
  async track(event: string, properties?: Record<string, any>): Promise<void> {
    // Mock implementation - in production this would send to analytics service
    console.log('[Analytics]', event, properties);
  }

  async identify(userId: string, properties?: Record<string, any>): Promise<void> {
    console.log('[Analytics] Identify:', userId, properties);
  }
}