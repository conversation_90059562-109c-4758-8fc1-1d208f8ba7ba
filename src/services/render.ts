import { ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface RenderJob {
  id: string;
  projectId: string;
  state: 'queued' | 'processing' | 'done' | 'failed';
  movieId?: string;
  progress: number;
  createdAt: string;
  completedAt?: string;
  error?: string;
}

export interface RenderService {
  requestCompilation(projectId: string): Promise<ApiResponse<{ jobId: string }>>;
  getRenderStatus(jobId: string): Promise<ApiResponse<RenderJob>>;
}

export class MockRenderService implements RenderService {
  async requestCompilation(projectId: string): Promise<ApiResponse<{ jobId: string }>> {
    try {
      const jobId = `render_${Date.now()}`;
      const job: RenderJob = {
        id: jobId,
        projectId,
        state: 'queued',
        progress: 0,
        createdAt: new Date().toISOString()
      };
      
      const jobs = await mockStore.get<RenderJob[]>('render_jobs') || [];
      jobs.push(job);
      await mockStore.set('render_jobs', jobs);
      
      // Simulate render process
      this.simulateRenderProcess(jobId);
      
      return {
        success: true,
        data: { jobId }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to start compilation'
      };
    }
  }

  async getRenderStatus(jobId: string): Promise<ApiResponse<RenderJob>> {
    try {
      const jobs = await mockStore.get<RenderJob[]>('render_jobs') || [];
      const job = jobs.find(j => j.id === jobId);
      
      if (!job) {
        return {
          success: false,
          error: 'Render job not found'
        };
      }
      
      return {
        success: true,
        data: job
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get render status'
      };
    }
  }

  private async simulateRenderProcess(jobId: string) {
    const updateJob = async (updates: Partial<RenderJob>) => {
      const jobs = await mockStore.get<RenderJob[]>('render_jobs') || [];
      const jobIndex = jobs.findIndex(j => j.id === jobId);
      
      if (jobIndex >= 0) {
        jobs[jobIndex] = { ...jobs[jobIndex], ...updates };
        await mockStore.set('render_jobs', jobs);
      }
    };
    
    // Start processing
    setTimeout(async () => {
      await updateJob({ state: 'processing', progress: 0 });
      
      // Simulate progress updates
      const progressInterval = setInterval(async () => {
        const jobs = await mockStore.get<RenderJob[]>('render_jobs') || [];
        const job = jobs.find(j => j.id === jobId);
        
        if (job && job.state === 'processing') {
          const newProgress = Math.min(job.progress + Math.random() * 20, 100);
          
          if (newProgress >= 100) {
            clearInterval(progressInterval);
            const movieId = `movie_${Date.now()}`;
            await updateJob({
              state: 'done',
              progress: 100,
              movieId,
              completedAt: new Date().toISOString()
            });
          } else {
            await updateJob({ progress: newProgress });
          }
        } else {
          clearInterval(progressInterval);
        }
      }, 1000);
    }, 2000);
  }
}