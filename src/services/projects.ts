import { Project, ApiResponse } from '../entities';
import { mockStore } from './storage';
import { seedData } from './seed-data';

export interface CreateProjectInput {
  title: string;
  description: string;
  goals?: string;
  privacy: 'public' | 'approved';
  scenes: Array<{
    prompt: string;
    durationLimitSec?: number;
    guidance?: string;
  }>;
}

export interface ProjectsService {
  listProjects(): Promise<ApiResponse<Project[]>>;
  getProject(id: string): Promise<ApiResponse<Project>>;
  createProject(input: CreateProjectInput): Promise<ApiResponse<Project>>;
  getUserProjects(userId: string): Promise<ApiResponse<Project[]>>;
}

export class MockProjectsService implements ProjectsService {
  constructor() {
    this.initializeData();
  }

  private async initializeData() {
    const existing = await mockStore.get<Project[]>('projects');
    if (!existing) {
      await mockStore.set('projects', seedData.projects);
    }
  }

  async listProjects(): Promise<ApiResponse<Project[]>> {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      const projects = await mockStore.get<Project[]>('projects') || [];
      
      return {
        success: true,
        data: projects.filter(p => p.privacy === 'public')
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load projects'
      };
    }
  }

  async getProject(id: string): Promise<ApiResponse<Project>> {
    try {
      const projects = await mockStore.get<Project[]>('projects') || [];
      const project = projects.find(p => p.id === id);
      
      if (!project) {
        return {
          success: false,
          error: 'Project not found'
        };
      }
      
      return {
        success: true,
        data: project
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load project'
      };
    }
  }

  async createProject(input: CreateProjectInput): Promise<ApiResponse<Project>> {
    try {
      const projects = await mockStore.get<Project[]>('projects') || [];
      
      const project: Project = {
        id: `project_${Date.now()}`,
        ...input,
        scenes: input.scenes.map((scene, index) => ({
          id: `scene_${Date.now()}_${index}`,
          order: index,
          ...scene
        })),
        ownerId: 'current_user', // This would come from auth context
        createdAt: new Date().toISOString()
      };
      
      projects.push(project);
      await mockStore.set('projects', projects);
      
      return {
        success: true,
        data: project
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to create project'
      };
    }
  }

  async getUserProjects(userId: string): Promise<ApiResponse<Project[]>> {
    try {
      const projects = await mockStore.get<Project[]>('projects') || [];
      const userProjects = projects.filter(p => p.ownerId === userId);
      
      return {
        success: true,
        data: userProjects
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load user projects'
      };
    }
  }
}