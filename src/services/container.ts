import { AuthService, MockAuthService } from './auth';
import { ProjectsService, MockProjectsService } from './projects';
import { ScenesService, MockScenesService } from './scenes';
import { ParticipationService, MockParticipationService } from './participation';
import { UploadsService, MockUploadsService } from './uploads';
import { RenderService, MockRenderService } from './render';
import { MoviesService, MockMoviesService } from './movies';
import { ModerationService, MockModerationService } from './moderation';
import { NotificationsService, MockNotificationsService } from './notifications';
import { AnalyticsService, MockAnalyticsService } from './analytics';

export class ServiceContainer {
  private static instance: ServiceContainer;
  
  public auth: AuthService;
  public projects: ProjectsService;
  public scenes: ScenesService;
  public participation: ParticipationService;
  public uploads: UploadsService;
  public render: RenderService;
  public movies: MoviesService;
  public moderation: ModerationService;
  public notifications: NotificationsService;
  public analytics: AnalyticsService;

  private constructor() {
    // Initialize with mock services for development
    this.auth = new MockAuthService();
    this.projects = new MockProjectsService();
    this.scenes = new MockScenesService();
    this.participation = new MockParticipationService();
    this.uploads = new MockUploadsService();
    this.render = new MockRenderService();
    this.movies = new MockMoviesService();
    this.moderation = new MockModerationService();
    this.notifications = new MockNotificationsService();
    this.analytics = new MockAnalyticsService();
  }

  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  // Method to swap implementations for production
  public configure(services: Partial<{
    auth: AuthService;
    projects: ProjectsService;
    scenes: ScenesService;
    participation: ParticipationService;
    uploads: UploadsService;
    render: RenderService;
    movies: MoviesService;
    moderation: ModerationService;
    notifications: NotificationsService;
    analytics: AnalyticsService;
  }>) {
    Object.assign(this, services);
  }
}

export const services = ServiceContainer.getInstance();