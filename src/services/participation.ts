import { Participation, Clip, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface SubmitClipInput {
  sceneId: string;
  fileUri: string;
  metadata: {
    consent: boolean;
    duration?: number;
    size?: number;
  };
}

export interface ParticipationService {
  submitClip(input: SubmitClipInput): Promise<ApiResponse<{ participationId: string; clipId: string }>>;
  getMySubmissions(userId: string): Promise<ApiResponse<Participation[]>>;
  getSubmissionStatus(participationId: string): Promise<ApiResponse<Participation>>;
  getUserClips(userId: string): Promise<ApiResponse<Clip[]>>;
}

export class MockParticipationService implements ParticipationService {
  async submitClip(input: SubmitClipInput): Promise<ApiResponse<{ participationId: string; clipId: string }>> {
    try {
      const participationId = `participation_${Date.now()}`;
      const clipId = `clip_${Date.now()}`;
      
      const participation: Participation = {
        id: participationId,
        userId: 'current_user', // Would come from auth context
        projectId: '', // Would be derived from sceneId
        sceneId: input.sceneId,
        status: 'submitted',
        createdAt: new Date().toISOString()
      };
      
      const clip: Clip = {
        id: clipId,
        ownerId: 'current_user',
        sceneId: input.sceneId,
        uri: input.fileUri,
        createdAt: new Date().toISOString(),
        status: 'uploading',
        rights: {
          consent: input.metadata.consent
        },
        metadata: {
          duration: input.metadata.duration,
          size: input.metadata.size
        }
      };
      
      // Store participation
      const participations = await mockStore.get<Participation[]>('participations') || [];
      participations.push(participation);
      await mockStore.set('participations', participations);
      
      // Store clip
      const clips = await mockStore.get<Clip[]>('clips') || [];
      clips.push(clip);
      await mockStore.set('clips', clips);
      
      return {
        success: true,
        data: { participationId, clipId }
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to submit clip'
      };
    }
  }

  async getMySubmissions(userId: string): Promise<ApiResponse<Participation[]>> {
    try {
      const participations = await mockStore.get<Participation[]>('participations') || [];
      const userSubmissions = participations.filter(p => p.userId === userId);
      
      return {
        success: true,
        data: userSubmissions
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load submissions'
      };
    }
  }

  async getSubmissionStatus(participationId: string): Promise<ApiResponse<Participation>> {
    try {
      const participations = await mockStore.get<Participation[]>('participations') || [];
      const participation = participations.find(p => p.id === participationId);
      
      if (!participation) {
        return {
          success: false,
          error: 'Submission not found'
        };
      }
      
      return {
        success: true,
        data: participation
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load submission status'
      };
    }
  }

  async getUserClips(userId: string): Promise<ApiResponse<Clip[]>> {
    try {
      const clips = await mockStore.get<Clip[]>('clips') || [];
      const userClips = clips.filter(c => c.ownerId === userId);
      
      return {
        success: true,
        data: userClips
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load clips'
      };
    }
  }
}