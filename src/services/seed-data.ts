import { Project, Movie, User } from '../entities';

export const seedData = {
  projects: [
    {
      id: 'project_city_stories',
      title: 'City Stories',
      description: 'Capture the essence of urban life through collaborative storytelling. Share your unique perspective of the city.',
      goals: 'Create a diverse collection of urban experiences that showcase the vibrancy and diversity of city life.',
      privacy: 'public' as const,
      ownerId: 'user_creator',
      createdAt: '2024-01-15T10:00:00Z',
      imageUrl: 'https://images.pexels.com/photos/374870/pexels-photo-374870.jpeg?auto=compress&cs=tinysrgb&w=800',
      scenes: [
        {
          id: 'scene_morning_routine',
          order: 0,
          prompt: 'Show us your morning routine in the city - from coffee shops to commutes',
          durationLimitSec: 30,
          guidance: 'Focus on authentic moments that capture the rhythm of urban mornings'
        },
        {
          id: 'scene_hidden_spots',
          order: 1,
          prompt: 'Reveal a hidden gem in your neighborhood that others might not know about',
          durationLimitSec: 45,
          guidance: 'Could be a secret viewpoint, local eatery, or unique street art'
        },
        {
          id: 'scene_evening_energy',
          order: 2,
          prompt: 'Capture the energy of your city as the sun sets and night begins',
          durationLimitSec: 60,
          guidance: 'Show how your city transforms from day to night'
        }
      ]
    },
    {
      id: 'project_nature_moments',
      title: 'Nature Moments',
      description: 'Document the natural world around us, from urban parks to wilderness adventures.',
      privacy: 'public' as const,
      ownerId: 'user_creator2',
      createdAt: '2024-02-01T14:30:00Z',
      imageUrl: 'https://images.pexels.com/photos/1624496/pexels-photo-1624496.jpeg?auto=compress&cs=tinysrgb&w=800',
      scenes: [
        {
          id: 'scene_sunrise',
          order: 0,
          prompt: 'Capture a sunrise or sunset in nature',
          durationLimitSec: 30
        },
        {
          id: 'scene_wildlife',
          order: 1,
          prompt: 'Document wildlife in their natural habitat',
          durationLimitSec: 45
        }
      ]
    }
  ] as Project[],

  movies: [
    {
      id: 'movie_city_stories_v1',
      projectId: 'project_city_stories',
      version: '1.0',
      playbackUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      createdAt: '2024-01-20T16:00:00Z',
      visibility: 'public' as const,
      thumbnailUrl: 'https://images.pexels.com/photos/374870/pexels-photo-374870.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ] as Movie[]
};