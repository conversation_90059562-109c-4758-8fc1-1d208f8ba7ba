import { Scene, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface ScenesService {
  getScenes(projectId: string): Promise<ApiResponse<Scene[]>>;
  getScene(sceneId: string): Promise<ApiResponse<Scene>>;
}

export class MockScenesService implements ScenesService {
  async getScenes(projectId: string): Promise<ApiResponse<Scene[]>> {
    try {
      const projects = await mockStore.get('projects') || [];
      const project = projects.find((p: any) => p.id === projectId);
      
      if (!project) {
        return {
          success: false,
          error: 'Project not found'
        };
      }
      
      return {
        success: true,
        data: project.scenes || []
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load scenes'
      };
    }
  }

  async getScene(sceneId: string): Promise<ApiResponse<Scene>> {
    try {
      const projects = await mockStore.get('projects') || [];
      
      for (const project of projects) {
        const scene = project.scenes?.find((s: Scene) => s.id === sceneId);
        if (scene) {
          return {
            success: true,
            data: scene
          };
        }
      }
      
      return {
        success: false,
        error: 'Scene not found'
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load scene'
      };
    }
  }
}