import AsyncStorage from '@react-native-async-storage/async-storage';

export class MockDataStore {
  private static instance: MockDataStore;
  private data: Map<string, any> = new Map();
  
  private constructor() {}

  public static getInstance(): MockDataStore {
    if (!MockDataStore.instance) {
      MockDataStore.instance = new MockDataStore();
    }
    return MockDataStore.instance;
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      // Try memory first
      if (this.data.has(key)) {
        return this.data.get(key);
      }
      
      // Fallback to AsyncStorage
      const stored = await AsyncStorage.getItem(`yiqqi_${key}`);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.data.set(key, parsed);
        return parsed;
      }
      
      return null;
    } catch {
      return null;
    }
  }

  async set<T>(key: string, value: T): Promise<void> {
    this.data.set(key, value);
    try {
      await AsyncStorage.setItem(`yiqqi_${key}`, JSON.stringify(value));
    } catch {
      // Silent fail for web compatibility
    }
  }

  async remove(key: string): Promise<void> {
    this.data.delete(key);
    try {
      await AsyncStorage.removeItem(`yiqqi_${key}`);
    } catch {
      // Silent fail
    }
  }

  async clear(): Promise<void> {
    this.data.clear();
    try {
      const keys = await AsyncStorage.getAllKeys();
      const yiqqiKeys = keys.filter(key => key.startsWith('yiqqi_'));
      await AsyncStorage.multiRemove(yiqqiKeys);
    } catch {
      // Silent fail
    }
  }
}

export const mockStore = MockDataStore.getInstance();