import { Movie, ApiResponse } from '../entities';
import { mockStore } from './storage';
import { seedData } from './seed-data';

export interface MoviesService {
  getMovie(id: string): Promise<ApiResponse<Movie>>;
  listMovies(projectId?: string): Promise<ApiResponse<Movie[]>>;
  getPublicMovies(): Promise<ApiResponse<Movie[]>>;
}

export class MockMoviesService implements MoviesService {
  constructor() {
    this.initializeData();
  }

  private async initializeData() {
    const existing = await mockStore.get<Movie[]>('movies');
    if (!existing) {
      await mockStore.set('movies', seedData.movies);
    }
  }

  async getMovie(id: string): Promise<ApiResponse<Movie>> {
    try {
      const movies = await mockStore.get<Movie[]>('movies') || [];
      const movie = movies.find(m => m.id === id);
      
      if (!movie) {
        return {
          success: false,
          error: 'Movie not found'
        };
      }
      
      return {
        success: true,
        data: movie
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load movie'
      };
    }
  }

  async listMovies(projectId?: string): Promise<ApiResponse<Movie[]>> {
    try {
      const movies = await mockStore.get<Movie[]>('movies') || [];
      let filteredMovies = movies;
      
      if (projectId) {
        filteredMovies = movies.filter(m => m.projectId === projectId);
      }
      
      return {
        success: true,
        data: filteredMovies
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load movies'
      };
    }
  }

  async getPublicMovies(): Promise<ApiResponse<Movie[]>> {
    try {
      const movies = await mockStore.get<Movie[]>('movies') || [];
      const publicMovies = movies.filter(m => m.visibility === 'public');
      
      return {
        success: true,
        data: publicMovies
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to load public movies'
      };
    }
  }
}