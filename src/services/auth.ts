import { User, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface AuthService {
  login(email: string): Promise<ApiResponse<User>>;
  logout(): Promise<ApiResponse<void>>;
  getCurrentUser(): Promise<User | null>;
  ageCheck(birthdate: string): Promise<ApiResponse<{ eligible: boolean }>>;
  register(user: Omit<User, 'id'>): Promise<ApiResponse<User>>;
}

export class MockAuthService implements AuthService {
  private currentUser: User | null = null;

  async login(email: string): Promise<ApiResponse<User>> {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      let user = await this.getUserByEmail(email);
      
      if (!user) {
        // Create new user for first-time login
        user = {
          id: `user_${Date.now()}`,
          email,
          name: email.split('@')[0],
          roles: {}
        };
        await this.saveUser(user);
      }
      
      this.currentUser = user;
      await mockStore.set('current_user', user);
      
      return {
        success: true,
        data: user
      };
    } catch (error) {
      return {
        success: false,
        error: 'Login failed'
      };
    }
  }

  async logout(): Promise<ApiResponse<void>> {
    this.currentUser = null;
    await mockStore.remove('current_user');
    return { success: true };
  }

  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) return this.currentUser;
    
    const stored = await mockStore.get<User>('current_user');
    if (stored) {
      this.currentUser = stored;
    }
    
    return this.currentUser;
  }

  async ageCheck(birthdate: string): Promise<ApiResponse<{ eligible: boolean }>> {
    const birth = new Date(birthdate);
    const now = new Date();
    const age = now.getFullYear() - birth.getFullYear();
    const monthDiff = now.getMonth() - birth.getMonth();
    
    const actualAge = monthDiff < 0 || (monthDiff === 0 && now.getDate() < birth.getDate()) ? age - 1 : age;
    
    return {
      success: true,
      data: { eligible: actualAge >= 13 }
    };
  }

  async register(userData: Omit<User, 'id'>): Promise<ApiResponse<User>> {
    const user: User = {
      ...userData,
      id: `user_${Date.now()}`
    };
    
    await this.saveUser(user);
    this.currentUser = user;
    await mockStore.set('current_user', user);
    
    return {
      success: true,
      data: user
    };
  }

  private async getUserByEmail(email: string): Promise<User | null> {
    const users = await mockStore.get<User[]>('users') || [];
    return users.find(u => u.email === email) || null;
  }

  private async saveUser(user: User): Promise<void> {
    const users = await mockStore.get<User[]>('users') || [];
    const existingIndex = users.findIndex(u => u.id === user.id);
    
    if (existingIndex >= 0) {
      users[existingIndex] = user;
    } else {
      users.push(user);
    }
    
    await mockStore.set('users', users);
  }
}