import { UploadSession, ApiResponse } from '../entities';
import { mockStore } from './storage';

export interface UploadManager {
  id: string;
  onProgress(callback: (progress: number) => void): void;
  pause(): void;
  resume(): void;
  cancel(): void;
}

export interface UploadsService {
  startResumable(fileUri: string): Promise<UploadManager>;
  getUploadStatus(uploadId: string): Promise<ApiResponse<UploadSession>>;
}

export class MockUploadsService implements UploadsService {
  private activeUploads = new Map<string, MockUploadManager>();

  async startResumable(fileUri: string): Promise<UploadManager> {
    const uploadId = `upload_${Date.now()}`;
    const session: UploadSession = {
      id: uploadId,
      fileUri,
      progress: 0,
      status: 'pending'
    };
    
    const uploads = await mockStore.get<UploadSession[]>('uploads') || [];
    uploads.push(session);
    await mockStore.set('uploads', uploads);
    
    const manager = new MockUploadManager(uploadId, this.updateUploadProgress.bind(this));
    this.activeUploads.set(uploadId, manager);
    
    return manager;
  }

  async getUploadStatus(uploadId: string): Promise<ApiResponse<UploadSession>> {
    try {
      const uploads = await mockStore.get<UploadSession[]>('uploads') || [];
      const upload = uploads.find(u => u.id === uploadId);
      
      if (!upload) {
        return {
          success: false,
          error: 'Upload not found'
        };
      }
      
      return {
        success: true,
        data: upload
      };
    } catch (error) {
      return {
        success: false,
        error: 'Failed to get upload status'
      };
    }
  }

  private async updateUploadProgress(uploadId: string, progress: number, status: UploadSession['status'], error?: string) {
    const uploads = await mockStore.get<UploadSession[]>('uploads') || [];
    const uploadIndex = uploads.findIndex(u => u.id === uploadId);
    
    if (uploadIndex >= 0) {
      uploads[uploadIndex] = {
        ...uploads[uploadIndex],
        progress,
        status,
        error
      };
      await mockStore.set('uploads', uploads);
    }
  }
}

class MockUploadManager implements UploadManager {
  public id: string;
  private progressCallback?: (progress: number) => void;
  private intervalId?: NodeJS.Timeout;
  private currentProgress = 0;
  private isPaused = false;
  private isCancelled = false;

  constructor(
    uploadId: string,
    private updateProgress: (id: string, progress: number, status: UploadSession['status'], error?: string) => void
  ) {
    this.id = uploadId;
    this.startUpload();
  }

  onProgress(callback: (progress: number) => void): void {
    this.progressCallback = callback;
  }

  pause(): void {
    this.isPaused = true;
    this.updateProgress(this.id, this.currentProgress, 'paused');
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  resume(): void {
    if (this.isPaused && !this.isCancelled) {
      this.isPaused = false;
      this.updateProgress(this.id, this.currentProgress, 'uploading');
      this.startUpload();
    }
  }

  cancel(): void {
    this.isCancelled = true;
    this.updateProgress(this.id, this.currentProgress, 'failed', 'Cancelled by user');
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = undefined;
    }
  }

  private startUpload(): void {
    if (this.isCancelled || this.isPaused) return;
    
    this.updateProgress(this.id, this.currentProgress, 'uploading');
    
    this.intervalId = setInterval(() => {
      if (this.isCancelled || this.isPaused) return;
      
      this.currentProgress += Math.random() * 10;
      
      if (this.currentProgress >= 100) {
        this.currentProgress = 100;
        this.updateProgress(this.id, 100, 'completed');
        this.progressCallback?.(100);
        
        if (this.intervalId) {
          clearInterval(this.intervalId);
          this.intervalId = undefined;
        }
      } else {
        this.updateProgress(this.id, this.currentProgress, 'uploading');
        this.progressCallback?.(this.currentProgress);
      }
    }, 200);
  }
}