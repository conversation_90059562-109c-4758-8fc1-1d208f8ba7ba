import React from 'react';
import { 
  TouchableOpacity, 
  Text, 
  StyleSheet, 
  ViewStyle, 
  TextStyle,
  ActivityIndicator 
} from 'react-native';
import { useTheme } from '../../theme';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  style,
  textStyle
}: ButtonProps) {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    button: {
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      ...(size === 'sm' && {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        minHeight: 32
      }),
      ...(size === 'md' && {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        minHeight: 44
      }),
      ...(size === 'lg' && {
        paddingHorizontal: theme.spacing.lg,
        paddingVertical: theme.spacing.md,
        minHeight: 52
      }),
      ...(variant === 'primary' && {
        backgroundColor: disabled ? theme.colors.border : theme.colors.primary,
      }),
      ...(variant === 'secondary' && {
        backgroundColor: disabled ? theme.colors.border : theme.colors.secondary,
      }),
      ...(variant === 'outline' && {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: disabled ? theme.colors.border : theme.colors.primary,
      }),
      ...(variant === 'ghost' && {
        backgroundColor: 'transparent',
      }),
    },
    text: {
      fontSize: size === 'sm' ? theme.typography.sizes.sm : 
               size === 'lg' ? theme.typography.sizes.lg : 
               theme.typography.sizes.base,
      fontWeight: theme.typography.weights.semibold,
      ...(variant === 'primary' && {
        color: disabled ? theme.colors.textMuted : '#ffffff',
      }),
      ...(variant === 'secondary' && {
        color: disabled ? theme.colors.textMuted : '#ffffff',
      }),
      ...(variant === 'outline' && {
        color: disabled ? theme.colors.textMuted : theme.colors.primary,
      }),
      ...(variant === 'ghost' && {
        color: disabled ? theme.colors.textMuted : theme.colors.primary,
      }),
    },
    loading: {
      marginRight: theme.spacing.xs
    }
  });

  return (
    <TouchableOpacity
      style={[styles.button, style]}
      onPress={onPress}
      disabled={disabled || loading}
      accessibilityRole="button"
      accessibilityLabel={title}
      accessibilityState={{ disabled: disabled || loading }}
    >
      {loading && (
        <ActivityIndicator 
          size="small" 
          color={variant === 'outline' || variant === 'ghost' ? theme.colors.primary : '#ffffff'} 
          style={styles.loading}
        />
      )}
      <Text style={[styles.text, textStyle]}>{title}</Text>
    </TouchableOpacity>
  );
}