import React, { useEffect } from 'react';
import { View, Text, StyleSheet, Animated, PanGestureHandler, PanGestureHandlerGestureEvent } from 'react-native';
import { useUIStore } from '../../store';
import { useTheme } from '../../theme';
import Animated, { useAnimatedGestureHandler, useAnimatedStyle, useSharedValue, withSpring, runOnJS } from 'react-native-reanimated';

interface ToastProps {
  id: string;
  message: string;
  type: 'success' | 'error' | 'info';
}

export function Toast({ id, message, type }: ToastProps) {
  const { theme } = useTheme();
  const removeToast = useUIStore(state => state.removeToast);
  const translateY = useSharedValue(-100);
  const translateX = useSharedValue(0);

  useEffect(() => {
    translateY.value = withSpring(0);
  }, []);

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: (_, context) => {
      context.startX = translateX.value;
    },
    onActive: (event, context) => {
      translateX.value = context.startX + event.translationX;
    },
    onEnd: (event) => {
      if (Math.abs(event.translationX) > 100) {
        translateX.value = withSpring(event.translationX > 0 ? 300 : -300);
        translateY.value = withSpring(-100, undefined, () => {
          runOnJS(removeToast)(id);
        });
      } else {
        translateX.value = withSpring(0);
      }
    }
  });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: translateY.value },
        { translateX: translateX.value }
      ]
    };
  });

  const styles = StyleSheet.create({
    toast: {
      position: 'absolute',
      top: 50,
      left: theme.spacing.md,
      right: theme.spacing.md,
      backgroundColor: type === 'success' ? theme.colors.success :
                      type === 'error' ? theme.colors.error :
                      theme.colors.primary,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      zIndex: 1000,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 8
    },
    text: {
      color: '#ffffff',
      fontSize: theme.typography.sizes.base,
      fontWeight: theme.typography.weights.medium,
      textAlign: 'center'
    }
  });

  return (
    <PanGestureHandler onGestureEvent={gestureHandler}>
      <Animated.View style={[styles.toast, animatedStyle]}>
        <Text style={styles.text}>{message}</Text>
      </Animated.View>
    </PanGestureHandler>
  );
}

export function ToastContainer() {
  const toasts = useUIStore(state => state.toasts);

  return (
    <>
      {toasts.map(toast => (
        <Toast key={toast.id} {...toast} />
      ))}
    </>
  );
}