import React, { ReactNode } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useTheme } from '../../theme';

interface CardProps {
  children: ReactNode;
  style?: ViewStyle;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

export function Card({ children, style, padding = 'md' }: CardProps) {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    card: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.lg,
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
      ...(padding === 'sm' && { padding: theme.spacing.sm }),
      ...(padding === 'md' && { padding: theme.spacing.md }),
      ...(padding === 'lg' && { padding: theme.spacing.lg }),
      ...(padding === 'none' && { padding: 0 }),
    }
  });

  return (
    <View style={[styles.card, style]}>
      {children}
    </View>
  );
}