export interface User {
  id: string;
  name: string;
  email: string;
  birthdate?: string;
  avatarUrl?: string;
  roles: {
    owner?: boolean;
    moderator?: boolean;
  };
}

export interface Community {
  id: string;
  name: string;
  logoUrl?: string;
  moderators: string[];
}

export interface Scene {
  id: string;
  order: number;
  prompt: string;
  durationLimitSec?: number;
  guidance?: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  goals?: string;
  privacy: 'public' | 'approved';
  scenes: Scene[];
  ownerId: string;
  deadline?: string;
  createdAt: string;
  imageUrl?: string;
}

export interface Participation {
  id: string;
  userId: string;
  projectId: string;
  sceneId: string;
  status: 'invited' | 'submitted' | 'approved' | 'rejected';
  notes?: string;
  createdAt: string;
}

export interface Clip {
  id: string;
  ownerId: string;
  sceneId: string;
  uri: string;
  createdAt: string;
  status: 'draft' | 'uploading' | 'uploaded' | 'processing' | 'approved' | 'rejected';
  rights: {
    consent: boolean;
  };
  metadata?: {
    duration?: number;
    size?: number;
  };
}

export interface Movie {
  id: string;
  projectId: string;
  version: string;
  playbackUrl: string;
  createdAt: string;
  visibility: 'public' | 'approved';
  thumbnailUrl?: string;
}

export interface ModerationItem {
  id: string;
  targetType: 'clip' | 'movie';
  targetId: string;
  verdict?: 'approve' | 'reject';
  reason?: string;
  createdAt: string;
  reviewerId?: string;
}

export interface NotificationPayload {
  [key: string]: any;
}

export interface Notification {
  id: string;
  type: 'project_invitation' | 'clip_approved' | 'clip_rejected' | 'movie_ready' | 'project_deadline';
  payload: NotificationPayload;
  read: boolean;
  createdAt: string;
  userId: string;
}

export interface UploadSession {
  id: string;
  fileUri: string;
  progress: number;
  status: 'pending' | 'uploading' | 'paused' | 'completed' | 'failed';
  error?: string;
}

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
}