You are Bolt. Create a new Expo React Native (TypeScript) app called "yiqqi-rn" that scaffolds the core flows for a collaborative community-video app inspired by <PERSON><PERSON><PERSON>.

## Goals
- Build a production-ready foundation I can run immediately in <PERSON><PERSON>’s preview and later ship via Expo/EAS.
- Prioritize the “Participate → Upload → Compile → Share” flow with privacy controls.
- Keep all external services abstracted behind adapters with mock implementations.

## Tech & Project Setup
- Expo (latest) + TypeScript + expo-router (file-based navigation with tabs).
- State/data: TanStack Query for server state, Zustand for UI/local state.
- Internationalization: i18next + react-i18next with EN, FR, ES seed translations.
- Media: react-native-vision-camera (capture), react-native-video (playback).
- Uploads: background/resumable upload stub; simulate progress.
- Animations & gestures: react-native-reanimated, react-native-gesture-handler.
- UI kit: minimal, clean design using basic RN components + utility styles; dark/light themes.
- Notifications: stub module compatible with FCM/APNs (no real keys).
- Testing: Jest + React Native Testing Library; a sample Detox config file.
- Linting/format: ESLint + Prettier.
- EAS readiness: include eas.json with development and preview profiles.

## App Structure (expo-router)
- Tabs: (home), participate, my-movies, notifications, settings.
- Routes:
  / (HomeFeed) – shows discoverable Projects/Campaigns.
  /project/[id]/index (ProjectDetail) – scenario overview, scenes list, privacy/audience.
  /project/[id]/participate (Participate) – entry point to contribute.
  /project/[id]/scene/[sceneId] (SceneCapture) – capture/import/trim/preview; upload with resumable progress.
  /upload/[id] (UploadProgress) – background-safe progress w/ retry.
  /movie/[id] (MoviePreview) – HLS/MP4 playback + share sheet.
  /moderation/[projectId] (ModerationQueue) – owner validates/rejects clips (mock data).
  /settings (Settings) – privacy defaults, language switcher, account.

## Domain Model (TypeScript types in /src/entities)
- User { id; name; email; birthdate; avatarUrl?; roles: { owner?: boolean; moderator?: boolean } }
- Community { id; name; logoUrl?; moderators: string[] }
- Project { id; title; description; goals?; privacy: "public" | "approved"; scenes: Scene[]; ownerId; deadline?: string }
- Scene { id; order: number; prompt: string; durationLimitSec?: number; guidance?: string }
- Participation { id; userId; projectId; sceneId; status: "invited" | "submitted" | "approved" | "rejected"; notes?: string }
- Clip { id; ownerId; sceneId; uri; createdAt; status: "draft" | "uploading" | "uploaded" | "processing" | "approved" | "rejected"; rights: { consent: boolean } }
- Movie { id; projectId; version: string; playbackUrl; createdAt; visibility: "public" | "approved" }
- ModerationItem { id; targetType: "clip" | "movie"; targetId; verdict?: "approve" | "reject"; reason?: string; createdAt }
- Notification { id; type; payload; read: boolean; createdAt }

## Services Layer (adapter pattern in /src/services with index.ts exporting interfaces)
Provide interfaces plus mock adapters:
- auth.ts: login(email), logout(), getCurrentUser(), ageCheck(birthdate)
- projects.ts: listProjects(), getProject(id), createProject(input)
- scenes.ts: getScenes(projectId)
- participation.ts: submitClip({sceneId, fileUri, metadata}), getMySubmissions(), getStatus(submissionId)
- uploads.ts: startResumable(fileUri) => { id; onProgress(cb); pause(); resume(); cancel() }
- render.ts: requestCompilation(projectId) => { jobId }; getRenderStatus(jobId) => { state: "queued"|"processing"|"done"; movieId? }
- movies.ts: getMovie(id)
- moderation.ts: listPending(projectId), setVerdict({itemId, verdict, reason?})
- notifications.ts: subscribe(), list(), markRead(id)
- analytics.ts: track(event, props?) // no real vendor; mock implementation only
- i18n.ts: bootstrap i18next with EN/FR/ES resources; include a language switcher hook.

All service calls should be Promise-based, return typed results, and the mock adapters should read/write to an in-memory store with persistence via AsyncStorage to survive reloads. Include a simple dependency injection container so we can swap adapters later.

## Screens (functional requirements)
- HomeFeed: grid/list of Projects with title, short description, privacy badge; pull-to-refresh via TanStack Query; i18n strings.
- ProjectDetail: shows scenario, scenes[] with order & prompts; CTA: “Participate”; owner-only button “Moderate”.
- Participate: explains how to contribute; shows scenes and which ones I’ve already submitted to.
- SceneCapture:
    - Record or import a clip.
    - Trim to durationLimitSec if set.
    - Preview, add basic metadata (consent toggle), then Upload.
    - Use uploads adapter: show real-time progress, retry on failure; background-safe UX (keep progress visible if user navigates away).
- UploadProgress: visual progress with pause/resume/cancel; on completion -> submission status page.
- MoviePreview: HLS playback (or MP4 fallback) via react-native-video; share sheet; visibility badge.
- ModerationQueue (owner): list pending clips; approve/reject with reason; updates Participation/Clip status.
- Settings: language selector (EN/FR/ES), privacy default (public/approved), mock notifications toggle, account info (includes birthdate capture on first login for age-gating).

## State & Data
- TanStack Query for all async service calls with sensible query keys (["projects"], ["project", id], etc.), optimistic updates for moderation decisions.
- Zustand for ephemeral UI: camera permissions, current upload session, toasts/snackbars, theme.
- Provide sample seed data: 1 mock Project titled “City Stories” with 3 Scenes (prompts), several mock user submissions in various statuses, and 1 compiled Movie with a public playbackUrl (use a placeholder HLS URL).

## i18n
- Provide translation files for EN/FR/ES; wrap all visible strings; Settings screen toggles language at runtime.

## Theming & Accessibility
- Light/dark themes; large tap targets; VoiceOver/TalkBack labels; prefers-reduced-motion support; color-contrast safe components.

## Storage & Privacy
- Show a minimal privacy consent during signup explaining PII fields (name, email, birthdate) and a link placeholder to privacy policy.
- Per-project privacy setting (“public” vs “approved”) must gate Movie visibility in the UI and mocks.

## Testing
- Add sample unit tests for services and one screen (SceneCapture).
- Provide testing instructions in README and npm scripts: test, lint, typecheck, ios, android, start.

## Developer Experience
- Clear README with: prerequisites, how to run (npx expo start), how to switch service adapters, how to add a new locale, and how to export a build with EAS.
- Add a simple script to reset the mock data store.

Deliver all source files with sensible paths, working navigation, and no TypeScript errors. After generating, run the app and show the tabs and seed content.
