{"name": "yiqqi-rn", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"dev": "EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "typecheck": "tsc --noEmit", "ios": "expo run:ios", "android": "expo run:android", "start": "expo start", "reset-data": "node scripts/reset-mock-data.js"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.62.3", "@tanstack/react-query-devtools": "^5.62.3", "expo": "^53.0.0", "expo-av": "~15.1.4", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.3", "expo-font": "~13.3.2", "expo-haptics": "~14.1.3", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-media-library": "~17.1.3", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.2.0", "i18next": "^24.2.0", "lucide-react-native": "^0.475.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.2.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-share": "^12.0.2", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-video": "^6.10.1", "react-native-vision-camera": "^4.7.1", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.9.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "detox": "^20.30.4", "jest": "^29.7.0", "jest-expo": "~53.0.9", "typescript": "~5.8.3"}}