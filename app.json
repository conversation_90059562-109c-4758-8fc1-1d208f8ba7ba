{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "yiqqi-rn", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "yiqqi", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "<PERSON><PERSON><PERSON> needs access to your camera to record videos for collaborative projects.", "NSMicrophoneUsageDescription": "<PERSON><PERSON><PERSON> needs access to your microphone to record audio with your videos.", "NSPhotoLibraryUsageDescription": "<PERSON><PERSON><PERSON> needs access to your photo library to import videos for collaborative projects."}}, "android": {"permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", "expo-web-browser", ["react-native-vision-camera", {"cameraPermissionText": "<PERSON><PERSON><PERSON> needs access to your Camera to record videos for collaborative projects.", "enableMicrophonePermission": true, "microphonePermissionText": "<PERSON><PERSON><PERSON> needs access to your Microphone to record audio with your videos."}]], "experiments": {"typedRoutes": true}}}